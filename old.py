import json
import os
import sys
import time
import re
import logging
import traceback
import requests
from requests.exceptions import ConnectionError
from concurrent.futures import ThreadPoolExecutor
from colorama import init, Fore, Style
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
from functools import lru_cache

init(autoreset=True)

# Paths dan konfigurasi
PATHS = {
    "working": "working_cookies",
    "expired": "expired_cookies",
    "canceled": "canceled_cookies",
    "on_hold": "on_hold_cookies",
    "error": "error_cookies"
}
LOG_FILE_PATH = "catatan_error.txt"
INPUT_COOKIES_FOLDER = "cookies"
NUM_THREADS = os.cpu_count() * 2
MAX_RETRIES = 2
TIMEOUT = 20
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36"

# FILTER KONFIGURASI: Aktifkan filter untuk email dan negara yang tidak valid
FILTER_INVALID_DATA = True  # Set False untuk menonaktifkan filter

# Pre-compiled regex patterns untuk efisiensi
EMAIL_PATTERN = re.compile(r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}")
COUNTRY_PATTERN = re.compile(r'"countryOfSignup":\s*"([^"]+)"')
SIGN_IN_PATTERN = re.compile(r"Sign [Ii]n")

# Status tracking
stats = {"working": 0, "expired": 0, "exceptions": 0, "canceled": 0, "on_hold": 0, "error": 0, "filtered": 0}

# Logger setup
logger = logging.getLogger(__name__)

def setup_logging():
    """Mengkonfigurasi logging ke file."""
    logger.setLevel(logging.INFO)
    try:
        fh = logging.FileHandler(LOG_FILE_PATH, mode='a', encoding='utf-8')
        fh.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        fh.setFormatter(formatter)
        logger.addHandler(fh)
    except IOError as e:
        # Fallback jika handler file log gagal dibuat
        print(f"{Fore.RED}[KRITIS] Gagal mengatur handler file log untuk '{LOG_FILE_PATH}': {e}{Style.RESET_ALL}", file=sys.stderr)

def log(msg, color=Fore.RESET, style=Style.NORMAL):
    print(f"{style}{color}{msg}{Style.RESET_ALL}")

def load_cookies(filepath):
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            content = f.read().strip() # .strip() untuk menangani spasi ekstra
        try:
            return json.loads(content), "json"
        except json.JSONDecodeError:
            if "# Netscape HTTP Cookie File" in content or "\t" in content:
                cookies = [
                    {
                        'name': parts[5], 'value': parts[6], 'domain': parts[0],
                        'path': parts[2], 'expirationDate': int(parts[4]) if parts[4].isdigit() else 0,
                        'secure': parts[3].lower() == 'true'
                    }
                    for line in content.splitlines()
                    if not (line.startswith('#') or not line.strip())
                    for parts in [line.strip().split('\t')]
                    if len(parts) >= 7
                ]
                if cookies:
                    return cookies, "netscape"
            log(f"[⚠️ ] Invalid format: {filepath}", Fore.RED)
            return None, "invalid"
    except Exception as e:
        log(f"[⚠️ ] File error saat membaca: {filepath}", Fore.RED)
        logger.error(f"Error membaca/mengidentifikasi {filepath}", exc_info=True)
        return None, "error"

def extend_expiry(cookies):
    expiration_timestamp = int((datetime.now() + timedelta(days=3*365)).timestamp())
    for cookie_item in cookies:
        for key_name in ["expirationDate", "expires", "expiry"]:
            if key_name in cookie_item:
                cookie_item[key_name] = expiration_timestamp
    return cookies

def save_cookie(path, cookies):
    try:
        with open(path, "w", encoding="utf-8") as f:
            json.dump(cookies, f, indent=4)
    except Exception as e:
        log(f"[⚠️ ] Error menyimpan file cookie {path}: {e}", Fore.RED)
        logger.error(f"Error menyimpan file cookie {path}", exc_info=True)

def ensure_folder(folder):
    os.makedirs(folder, exist_ok=True)

def unique_path(filepath):
    if not os.path.exists(filepath):
        return filepath
    base, ext = os.path.splitext(filepath)
    counter = 1
    while os.path.exists(new_path := f"{base}_{counter}{ext}"):
        counter += 1
    return new_path

def move_file(src, dst_folder, dst_name):
    ensure_folder(dst_folder)
    dst = unique_path(os.path.join(dst_folder, dst_name))
    try:
        os.rename(src, dst)
    except Exception as e:
        log(f"[⚠️ ] Error memindahkan file: {src} -> {dst}: {e}", Fore.RED)
        logger.error(f"Error memindahkan file {src} ke {dst}", exc_info=True)

def extract_info(text):
    match = COUNTRY_PATTERN.search(text)
    return {"countryOfSignup": match.group(1) if match else "Unknown"}

@lru_cache(maxsize=128)
def search_plan(text):
    if not text:
        return "Unknown"
    text_lower = text.lower()
    for plan in ["Premium", "Standard", "Basic", "Mobile"]:
        if plan.lower() in text_lower:
            return plan
    return "Unknown"

def get_account_info(session, filename):
    try:
        # LOGIKA PENTING: Ambil email dari halaman security
        r_sec = session.get("https://www.netflix.com/account/security", timeout=TIMEOUT)
        email_match = EMAIL_PATTERN.search(r_sec.text)
        email = email_match.group(0) if email_match else "Unknown"

        # LOGIKA PENTING: Cek halaman akun utama
        r_acc = session.get("https://www.netflix.com/YourAccount", timeout=TIMEOUT)

        # LOGIKA KRITIS: Deteksi cookie expired dengan pattern yang lebih efisien
        if SIGN_IN_PATTERN.search(r_acc.text):
            raise Exception("Cookie Expired")

        soup = BeautifulSoup(r_acc.content, "html.parser")

        # LOGIKA PENTING: Cek status akun dibatalkan
        canceled_element = soup.select_one('h3[data-uia="account-overview-page+former-member-card+title"]')
        if canceled_element:
            return "Canceled", "Unknown", email, False

        # LOGIKA PENTING: Cek status akun ditahan
        on_hold = False
        alert_element = soup.select_one('[role="alert"][data-uia^="account-overview-page"]')
        if alert_element:
            update_payment_button = alert_element.select_one('button[data-uia*="UPDATE_PAYMENT"]')
            if update_payment_button:
                on_hold = True
            else:
                payment_keywords = ["update your payment information", "perbarui informasi pembayaran"]
                alert_text_lower = alert_element.text.lower()
                if any(keyword in alert_text_lower for keyword in payment_keywords):
                    on_hold = True
        
        if not on_hold and soup.select_one('p.default-ltr-cache-1av505g'): # Elemen on-hold lain
            on_hold = True

        if on_hold:
            return "On Hold", "X", email, False

        # Ambil informasi plan dan negara
        plan_elem = soup.select_one('h3[data-uia^="account-overview-page+membership-card+title"]')
        plan = plan_elem.text.strip() if plan_elem else search_plan(r_acc.text)
        country = extract_info(r_acc.text).get("countryOfSignup", "Unknown")
        if FILTER_INVALID_DATA and (email == "Unknown" or country == "Unknown"):
            logger.warning(f"Data tidak valid difilter untuk {filename}: email={email}, country={country}")
            raise Exception("Cookie Expired - Invalid email or country data")

        # Cek extra member support
        extra = False
        try:
            r_extra = session.get(
                "https://www.netflix.com/accountowner/addextramember",
                allow_redirects=False, headers={"User-Agent": USER_AGENT}, timeout=TIMEOUT
            )
            extra = r_extra.status_code == 200
        except Exception:
            # Gagal cek extra member tidak fatal, log jika perlu tapi jangan hentikan proses
            logger.debug(f"Gagal memeriksa status extra member untuk {filename}, mengasumsikan False.", exc_info=False) # exc_info=False karena ini bukan error utama

        return plan, country, email, extra

    except Exception as e:
        logger.error(f"Error mendapatkan info akun untuk {filename}", exc_info=True)
        # LOGIKA KRITIS: Deteksi cookie expired dari exception
        if "Cookie Expired" in str(e) or isinstance(e, ConnectionError):
            raise Exception("Cookie Expired")
        return None, None, None, False

def handle_status(filepath, cookies, file_type, status, filename, email, plan, country, extra):
    base = os.path.splitext(filename)[0]
    # Konfigurasi status dengan mapping yang efisien
    status_config = {
        "Canceled": {"folder": PATHS["canceled"], "color": Fore.RED, "emoji": "❌", "stat_key": "canceled"},
        "On Hold": {"folder": PATHS["on_hold"], "color": Fore.YELLOW, "emoji": "⏸️", "stat_key": "on_hold"},
        "Working": {"folder": PATHS["working"], "color": Fore.GREEN, "emoji": "✔️ ", "stat_key": "working"}
    }

    config = status_config[status]

    if status in ["Canceled", "On Hold"]:
        log(f"[{config['emoji']}] {status} - {filename}", config["color"])
        stats[config["stat_key"]] += 1
        move_file(filepath, config["folder"], f"{base}.{file_type}")
    else:  # Working
        extra_info = " (Extra Member)" if extra else ""
        safe_plan, safe_email, safe_country = plan.replace("/", "-"), email.split('@')[0] if email != "Unknown" else "Unknown", country.replace("/", "-")
        new_name = f"[{safe_plan}] - {safe_email} - [{safe_country}]{extra_info}.json"
        dest = unique_path(os.path.join(config["folder"], new_name))
        save_cookie(dest, cookies)
        log(f"[{config['emoji']}] Working - {filename} -> {os.path.basename(dest)}", config["color"])
        stats[config["stat_key"]] += 1
        try:
            os.remove(filepath)
        except Exception as e:
            log(f"[⚠️ ] Error menghapus file asli: {filename} - {e}", Fore.RED)
            logger.error(f"Error menghapus file asli {filepath}", exc_info=True)

def process_cookie(filepath):
    filename = os.path.basename(filepath)
    cookies, file_type = load_cookies(filepath)

    # LOGIKA PENTING: Validasi cookies
    if not cookies or file_type == "invalid":
        stats["exceptions"] += 1 # Seharusnya ini juga menambah stats["error"] jika file dipindah ke error
        log(f"[💥] Invalid: {filename} -> error folder.", Fore.MAGENTA)
        move_file(filepath, PATHS["error"], filename)
        stats["error"] += 1
        return

    cookies = extend_expiry(cookies)

    try:
        with requests.Session() as s:
            s.headers.update({"User-Agent": USER_AGENT})
            # Set cookies ke session dengan validasi
            for c in cookies:
                if all(k in c for k in ("name", "value", "domain")):
                    s.cookies.set(
                        name=c["name"], value=c["value"], domain=c["domain"],
                        path=c.get("path", "/"), secure=c.get("secure", False),
                        expires=c.get("expires") or c.get("expirationDate")
                    )

            # LOGIKA KRITIS: Retry mechanism untuk stabilitas
            for attempt in range(MAX_RETRIES):
                try:
                    info = get_account_info(s, filename)
                    if info[0] is None:
                        raise Exception("No account info")
                    break
                except Exception as e:
                    # LOGIKA KRITIS: Deteksi cookie expired
                    if "Cookie Expired" in str(e):
                        # Cek apakah ini karena filter data invalid
                        if "Invalid email or country data" in str(e):
                            log(f"[🚫] Filtered (Invalid Data) - {filename}", Fore.YELLOW)
                            stats["filtered"] += 1
                        else:
                            log(f"[❌ ] Expired - {filename}", Fore.RED)
                            stats["expired"] += 1
                        move_file(filepath, PATHS["expired"], filename)
                        return

                    # LOGIKA PENTING: Max retries handling
                    if attempt == MAX_RETRIES - 1:
                        log(f"[💥] Max retries tercapai untuk {filename}. Dipindahkan ke folder error.", Fore.MAGENTA)
                        logger.error(f"Max retries tercapai untuk {filename}", exc_info=True)
                        stats["exceptions"] += 1
                        move_file(filepath, PATHS["error"], filename)
                        stats["error"] += 1
                        return

                    time.sleep(2)

            # LOGIKA PENTING: Klasifikasi status akun
            plan, country, email, extra = info
            status = plan if plan in ["Canceled", "On Hold"] else "Working"
            handle_status(filepath, cookies, file_type, status, filename, email, plan, country, extra)

    except Exception as e:
        log(f"[💥] Error tak terduga saat memproses {filename}. Dipindahkan ke folder error.", Fore.MAGENTA)
        logger.error(f"Error tak terduga saat memproses {filename}", exc_info=True)
        stats["exceptions"] += 1
        move_file(filepath, PATHS["error"], filename)
        stats["error"] += 1

def main():
    t0 = time.time()
    separator = "="*70
    setup_logging() # Panggil setup logging di awal
    log(separator, Fore.CYAN, Style.BRIGHT)
    log("🍪 Netflix Cookie Checker 🍪", Fore.CYAN, Style.BRIGHT)
    log(separator, Fore.CYAN, Style.BRIGHT)

    # Tampilkan status filter
    filter_status = "ENABLED" if FILTER_INVALID_DATA else "DISABLED"
    filter_color = Fore.GREEN if FILTER_INVALID_DATA else Fore.RED
    log(f"🚫 Invalid Data Filter: {filter_status}", filter_color)

    # Pastikan semua folder output ada
    for folder in PATHS.values():
        ensure_folder(folder)

    # LOGIKA PENTING: Validasi folder input
    if not os.path.isdir(INPUT_COOKIES_FOLDER):
        log(f"[⚠️ ] Folder '{INPUT_COOKIES_FOLDER}' not found.", Fore.RED, Style.BRIGHT)
        sys.exit(1)

    # LOGIKA PENTING: Ambil daftar file cookie
    try:
        files = [
            os.path.join(INPUT_COOKIES_FOLDER, f)
            for f in os.listdir(INPUT_COOKIES_FOLDER)
            if os.path.isfile(os.path.join(INPUT_COOKIES_FOLDER, f))
        ]
    except Exception as e:
        log(f"[⚠️ ] Error membaca folder input '{INPUT_COOKIES_FOLDER}': {e}", Fore.RED)
        logger.error(f"Error membaca folder input '{INPUT_COOKIES_FOLDER}'", exc_info=True)
        sys.exit(1)

    if not files:
        log(f"[ℹ️ ] No cookie files in '{INPUT_COOKIES_FOLDER}'.", Fore.YELLOW)
        sys.exit(0)

    log(f"[🔍] {len(files)} files found.", Fore.CYAN)
    log(f"[🚀] Processing with {NUM_THREADS} threads...", Fore.CYAN)

    # LOGIKA KRITIS: Pemrosesan paralel dengan error handling
    try:
        with ThreadPoolExecutor(max_workers=NUM_THREADS) as executor:
            list(executor.map(process_cookie, files))
    except Exception as e:
        log(f"[⚠️ ] Terjadi error saat pemrosesan paralel: {e}", Fore.RED)
        logger.error("Error selama eksekusi ThreadPoolExecutor", exc_info=True)

    # Tampilkan ringkasan hasil
    total, elapsed = sum(stats.values()), time.time() - t0
    log(f"\n{separator}", Fore.CYAN, Style.BRIGHT)
    log("📊 Summary 📊", Fore.CYAN, Style.BRIGHT)
    log(separator, Fore.CYAN, Style.BRIGHT)

    # Summary items dengan mapping yang efisien
    summary_data = [
        ("Total", len(files), Fore.YELLOW),
        ("✔️  Working", stats['working'], Fore.GREEN),
        ("❌ Expired", stats['expired'], Fore.RED),
        ("🚫 Filtered", stats['filtered'], Fore.YELLOW),
        ("⏸️  On Hold", stats['on_hold'], Fore.YELLOW),
        ("❌ Canceled", stats['canceled'], Fore.RED),
        ("💥 Error", stats['error'], Fore.MAGENTA),
        ("⚠️  Exceptions", stats['exceptions'], Fore.MAGENTA),
    ]

    for label, value, color in summary_data:
        log(f"{label}: {value}", color)

    if total != len(files):
        log(f"[⚠️ ] Mismatch: {len(files)} files, {total} outcomes.", Fore.RED)

    log(f"⏱️  Done in {elapsed:.2f}s.", Fore.CYAN)
    log(f"📝 See '{LOG_FILE_PATH}' for errors.", Fore.CYAN)
    log(separator, Fore.CYAN, Style.BRIGHT)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        log("\n[⚠️ ] Interrupted by user.", Fore.RED)
        sys.exit(1)
    except Exception as e:
        log(f"[⚠️ ] Terjadi error kritis tak terduga: {e}", Fore.RED)
        logger.critical("Error kritis tak terduga di __main__", exc_info=True)
        sys.exit(1)